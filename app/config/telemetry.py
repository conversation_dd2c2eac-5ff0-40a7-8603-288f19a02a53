"""
OpenTelemetry Configuration

This module configures OpenTelemetry for distributed tracing in the FastAPI application.
It sets up trace providers, instrumentations, and trace ID handling.
"""

import logging
from typing import Optional

from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor

from app.config.settings import settings

# Global tracer instance
tracer: Optional[trace.Tracer] = None


def setup_telemetry() -> None:
    """
    Set up OpenTelemetry tracing configuration.
    
    Configures the tracer provider, span processors, and instrumentations
    for FastAPI and SQLAlchemy.
    """
    global tracer
    
    # Set up tracer provider
    trace.set_tracer_provider(TracerProvider())
    
    # Add console exporter for development
    if settings.ENVIRONMENT == "development":
        console_exporter = ConsoleSpanExporter()
        span_processor = BatchSpanProcessor(console_exporter)
        trace.get_tracer_provider().add_span_processor(span_processor)
    
    # Get tracer instance
    tracer = trace.get_tracer(__name__)
    
    # Set up SQLAlchemy instrumentation
    SQLAlchemyInstrumentor().instrument()


def instrument_fastapi(app) -> None:
    """
    Instrument FastAPI application with OpenTelemetry.
    
    Args:
        app: FastAPI application instance
    """
    FastAPIInstrumentor.instrument_app(app)


def get_tracer() -> trace.Tracer:
    """
    Get the global tracer instance.
    
    Returns:
        OpenTelemetry tracer instance
    """
    global tracer
    if tracer is None:
        setup_telemetry()
    return tracer


def get_current_trace_id() -> Optional[str]:
    """
    Get the current trace ID from the active span context.
    
    Returns:
        Trace ID as hex string, or None if no active trace
    """
    try:
        current_span = trace.get_current_span()
        if current_span and current_span.get_span_context().is_valid:
            trace_id = current_span.get_span_context().trace_id
            return format(trace_id, '032x')  # Convert to 32-character hex string
        return None
    except Exception:
        return None


def get_current_span_id() -> Optional[str]:
    """
    Get the current span ID from the active span context.
    
    Returns:
        Span ID as hex string, or None if no active span
    """
    try:
        current_span = trace.get_current_span()
        if current_span and current_span.get_span_context().is_valid:
            span_id = current_span.get_span_context().span_id
            return format(span_id, '016x')  # Convert to 16-character hex string
        return None
    except Exception:
        return None


def create_span(name: str, **attributes) -> trace.Span:
    """
    Create a new span with the given name and attributes.
    
    Args:
        name: Span name
        **attributes: Additional span attributes
        
    Returns:
        New span instance
    """
    tracer = get_tracer()
    span = tracer.start_span(name)
    
    # Add attributes to span
    for key, value in attributes.items():
        span.set_attribute(key, value)
    
    return span
